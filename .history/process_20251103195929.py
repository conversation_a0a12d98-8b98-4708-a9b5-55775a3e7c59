import pandas as pd
import csv
import random
import numpy as np
import matplotlib.pyplot as plt
import japanize_matplotlib
import os
import glob
import time

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []

# コストとペナルティの係数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 400
出荷遅れコスト単価 = 500

定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
段替え時間 = 30

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    収容数辞書 = {}
    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:
        capacity_reader = csv.reader(capacity_file)
        capacity_header = next(capacity_reader)
        for row in capacity_reader:
            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty
                品番 = row[0]  # 品番列
                収容数 = int(float(row[1]))  # 収容数列
                収容数辞書[品番] = 収容数

    with open(file_path, 'r', encoding='shift-jis') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        # 期間数（日数）を定義
        期間数 = 20
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            
            # 個数を取得
            total_quantity = int(row[header.index("個数")])
            
            # 個数が200未満の場合はスキップ
            if total_quantity < 200:
                continue
            
            # 1日あたりの出荷数を計算（総期間で割る）
            daily_quantity = total_quantity / 期間数
            
            品番リスト.append(row[header.index("素材品番")])
            出荷数リスト.append(daily_quantity)
            込め数リスト.append(int(float(row[header.index("込数")])))
            
            cycle_time_per_unit = float(row[header.index("サイクルタイム")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            収容数リスト.append(収容数辞書.get(品番, 80))
            
    
        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定
        初期在庫量リスト = []
        for shipment in 出荷数リスト:
            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))
            初期在庫量リスト.append(random_inventory)
            
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def load_initial_inventory_from_csv(file_name, inventory_folder='initial_inventory'):
    """CSVファイルから初期在庫を読み込む関数"""
    csv_path = os.path.join(inventory_folder, f'initial_inventory_{file_name}.csv')

    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"初期在庫CSVファイルが見つかりません: {csv_path}")

    df = pd.read_csv(csv_path, encoding='utf-8-sig')

    # 調整後初期在庫を返す
    adjusted_inventory = df['調整後初期在庫'].tolist()

    print(f"初期在庫CSVファイルから読み込み: {csv_path}")
    print(f"調整後初期在庫合計: {sum(adjusted_inventory)}")

    return adjusted_inventory

def solve_mip(initial_inventory_list_arg, time_limit=500):
    """PuLPを用いてMIPを解く関数"""
    import pulp

    # モデルの定義
    model = pulp.LpProblem("ProductionScheduling", pulp.LpMinimize)

    # インデックスの定義
    品番数 = len(品番リスト)
    期間 = 20
    品目 = range(品番数)
    期間_index = range(期間)

    # 決定変数
    Production = pulp.LpVariable.dicts("Production", (品目, 期間_index), lowBound=0, cat='Integer')
    IsProduced = pulp.LpVariable.dicts("IsProduced", (品目, 期間_index), cat='Binary')
    Inventory = pulp.LpVariable.dicts("Inventory", (品目, 期間_index), lowBound=0, cat='Continuous')
    Shortage = pulp.LpVariable.dicts("Shortage", (品目, 期間_index), lowBound=0, cat='Continuous')
    WorkTime = pulp.LpVariable.dicts("WorkTime", 期間_index, lowBound=0, cat='Continuous')
    Overtime = pulp.LpVariable.dicts("Overtime", 期間_index, lowBound=0, cat='Continuous')

    # 目的関数
    total_cost = pulp.lpSum(
        在庫コスト単価 * Inventory[i][t]/収容数リスト[i] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        残業コスト単価 * Overtime[t] for t in 期間_index
    ) + pulp.lpSum(
        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index
    )

    model += total_cost, "Total Cost"

    # 制約条件
    bigM = 1000000

    for i in 品目:
        for t in 期間_index:
            if t == 0:
                # 初期在庫リストを使用
                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]
            else:
                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]

            model += Production[i][t] <= bigM * IsProduced[i][t]

    for t in 期間_index:
        model += WorkTime[t] == pulp.lpSum(
            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]
            for i in 品目
        )

        model += WorkTime[t] <= 定時 + Overtime[t]
        model += WorkTime[t] <= 定時 + 最大残業時間
        model += Overtime[t] >= WorkTime[t] - 定時
        model += Overtime[t] >= 0

    # Solverの設定
    try:
        solver = pulp.GUROBI(msg=True, timelimit=time_limit)
    except:
        solver = pulp.PULP_CBC_CMD(msg=True, timeLimit=time_limit)

    # 最適化の実行
    model.solve(solver)

    # ソルバーの詳細情報を取得
    status = pulp.LpStatus[model.status]
    print("ステータス:", status)

    accuracy_info = {}

    if status == 'Optimal' or (status == 'Not Solved' and pulp.value(model.objective) is not None):
        if status == 'Not Solved':
            print("時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました")

        print("総コスト:", pulp.value(model.objective))

        production_schedule = [[0] * 期間 for _ in range(品番数)]
        for i in 品目:
            for t in 期間_index:
                production_schedule[i][t] = pulp.value(Production[i][t])

        return production_schedule, pulp.value(model.objective), accuracy_info

    return None, None, accuracy_info

def simulated_annealing_scheduler(initial_inventory, max_iterations=1000, initial_temp=1000, cooling_rate=0.95):
    """焼きなまし法による生産スケジューリング"""
    品番数 = len(initial_inventory)
    期間 = 20

    # 初期解の生成（貪欲法）
    def generate_initial_solution():
        schedule = [[0] * 期間 for _ in range(品番数)]
        inventory = initial_inventory[:]

        for t in range(期間):
            for i in range(品番数):
                inventory[i] -= 出荷数リスト[i]
                if inventory[i] < 0:
                    production = abs(inventory[i])
                    schedule[i][t] = production
                    inventory[i] += production

        return schedule

    # 近傍解の生成
    def generate_neighbor(solution):
        new_solution = [row[:] for row in solution]

        # ランダムに品番と期間を選択
        i = random.randint(0, 品番数 - 1)
        t = random.randint(0, 期間 - 1)

        # 生産量を調整
        change = random.randint(-10, 10)
        new_solution[i][t] = max(0, new_solution[i][t] + change)

        return new_solution

    # 現在の解
    current_solution = generate_initial_solution()
    current_cost = calculate_total_cost(current_solution, initial_inventory)

    # 最良解
    best_solution = [row[:] for row in current_solution]
    best_cost = current_cost

    # 焼きなまし法のメインループ
    temperature = initial_temp

    for iteration in range(max_iterations):
        # 近傍解を生成
        neighbor_solution = generate_neighbor(current_solution)
        neighbor_cost = calculate_total_cost(neighbor_solution, initial_inventory)

        # 解の受容判定
        if neighbor_cost < current_cost:
            # より良い解なら受容
            current_solution = neighbor_solution
            current_cost = neighbor_cost

            # 最良解の更新
            if current_cost < best_cost:
                best_solution = [row[:] for row in current_solution]
                best_cost = current_cost
        else:
            # 悪い解でも確率的に受容
            delta = neighbor_cost - current_cost
            probability = np.exp(-delta / temperature)

            if random.random() < probability:
                current_solution = neighbor_solution
                current_cost = neighbor_cost

        # 温度を下げる
        temperature *= cooling_rate

        # 温度が十分低くなったら終了
        if temperature < 1e-6:
            break

    return best_solution, best_cost

def multi_start_simulated_annealing(initial_inventory, num_starts=5, **sa_params):
    """多スタート焼きなまし法"""
    best_solution = None
    best_cost = float('inf')

    print("=== 多スタート焼きなまし法 スケジューリング ===")

    for start in range(num_starts):
        print(f"--- Start {start + 1}/{num_starts} ---")

        solution, cost = simulated_annealing_scheduler(initial_inventory, **sa_params)

        if cost < best_cost:
            best_solution = solution
            best_cost = cost
            print(f"  New best solution found with total cost: {cost:.2f}")

    return best_solution, best_cost

def calculate_total_cost(production_schedule, initial_inventory, 期間=20):
    """総コストを計算する関数"""
    品番数 = len(initial_inventory)
    
    # 在庫コスト
    inventory_cost = 0
    inventory = initial_inventory[:]
    
    # セットアップコスト
    setup_cost = 0
    
    # 残業コスト
    overtime_cost = 0
    
    # 出荷遅れコスト
    shortage_cost = 0
    
    for t in range(期間):
        daily_work_time = 0
        
        for i in range(品番数):
            # 生産
            production = production_schedule[i][t] if production_schedule else 0
            
            # セットアップコスト
            if production > 0:
                setup_cost += 段替えコスト単価
                daily_work_time += 段替え時間
                daily_work_time += (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            
            # 在庫更新
            inventory[i] += production
            inventory[i] -= 出荷数リスト[i]
            
            # 在庫コスト
            if inventory[i] > 0:
                inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価
            
            # 出荷遅れコスト
            if inventory[i] < 0:
                shortage_cost += abs(inventory[i]) * 出荷遅れコスト単価
                inventory[i] = 0
        
        # 残業コスト + 制約違反ペナルティ
        if daily_work_time > 定時:
            if daily_work_time <= 定時 + 最大残業時間:
                # 正常な残業範囲内
                overtime = daily_work_time - 定時
                overtime_cost += overtime * 残業コスト単価
            else:
                # 制約違反：最大残業時間を超過
                # 正常な残業分のコスト
                overtime = 最大残業時間
                overtime_cost += overtime * 残業コスト単価

                # 制約違反分に対する大きなペナルティ
                violation_time = daily_work_time - (定時 + 最大残業時間)
                violation_penalty = violation_time * 残業コスト単価 * 1000  # 1000倍のペナルティ
                overtime_cost += violation_penalty
    
    total_cost = inventory_cost + setup_cost + overtime_cost + shortage_cost
    return total_cost

def plot_result(production_schedule, initial_inventory, file_name, method_name="", 期間=20):
    """結果をプロットする関数（4種類の棒グラフ版）"""
    import matplotlib.pyplot as plt
    import japanize_matplotlib
    import os
    import numpy as np

    品番数 = len(品番リスト)

    # 各期間のデータを計算
    working_times = []  # 稼働時間（生産時間+段替え時間）
    total_inventory = []  # 総在庫量
    total_production = []  # 総生産量
    total_shortage = []  # 総出荷遅れ量

    current_inventory = initial_inventory[:]
    max_daily_work_time = 定時 + 最大残業時間

    for t in range(期間):
        # 稼働時間の計算
        daily_working_time = 0
        period_production = 0
        period_shortage = 0

        if production_schedule:
            for i in range(品番数):
                if production_schedule[i][t] > 0:
                    # 段替え時間を追加
                    daily_working_time += 段替え時間
                    # 生産時間を追加
                    production_time = (production_schedule[i][t] / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_working_time += production_time
                    period_production += production_schedule[i][t]

        working_times.append(daily_working_time)
        total_production.append(period_production)

        # 在庫と出荷遅れの計算
        period_inventory = 0
        for i in range(品番数):
            # 生産量を在庫に追加
            if production_schedule:
                current_inventory[i] += production_schedule[i][t]

            # 出荷処理
            demand = 出荷数リスト[i]
            if current_inventory[i] >= demand:
                current_inventory[i] -= demand
            else:
                # 出荷遅れが発生
                shortage = demand - current_inventory[i]
                period_shortage += shortage
                current_inventory[i] = 0

            period_inventory += current_inventory[i]

        total_inventory.append(period_inventory)
        total_shortage.append(period_shortage)

    # 4つのサブプロットを作成
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    periods = range(1, 期間 + 1)

    # 1. 稼働時間の棒グラフ
    bars1 = ax1.bar(periods, working_times, color='skyblue', alpha=0.7)
    ax1.axhline(y=定時, color='green', linestyle='--', label=f'定時 ({定時}分)')
    ax1.axhline(y=max_daily_work_time, color='red', linestyle='--', label=f'最大稼働時間 ({max_daily_work_time}分)')
    ax1.set_title(f'稼働時間推移 ({method_name})', fontsize=14)
    ax1.set_xlabel('期間', fontsize=12)
    ax1.set_ylabel('稼働時間 (分)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 在庫量の棒グラフ
    bars2 = ax2.bar(periods, total_inventory, color='orange', alpha=0.7)
    ax2.set_title(f'総在庫量推移 ({method_name})', fontsize=14)
    ax2.set_xlabel('期間', fontsize=12)
    ax2.set_ylabel('総在庫量', fontsize=12)
    ax2.grid(True, alpha=0.3)

    # 3. 生産量の棒グラフ
    bars3 = ax3.bar(periods, total_production, color='lightgreen', alpha=0.7)
    ax3.set_title(f'総生産量推移 ({method_name})', fontsize=14)
    ax3.set_xlabel('期間', fontsize=12)
    ax3.set_ylabel('総生産量', fontsize=12)
    ax3.grid(True, alpha=0.3)

    # 4. 出荷遅れ量の棒グラフ
    bars4 = ax4.bar(periods, total_shortage, color='lightcoral', alpha=0.7)
    ax4.set_title(f'出荷遅れ量推移 ({method_name})', fontsize=14)
    ax4.set_xlabel('期間', fontsize=12)
    ax4.set_ylabel('出荷遅れ量', fontsize=12)
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    # 結果フォルダが存在しない場合は作成
    os.makedirs('result', exist_ok=True)

    # プロットを保存
    plot_filename = f'result/{method_name}_results_{file_name}.png' if method_name else f'result/results_{file_name}.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"プロットを保存: {plot_filename}")

    # 時間制約違反をチェック
    violation_count = sum(1 for wt in working_times if wt > max_daily_work_time)
    print(f"時間制約違反: {violation_count} 期間")

    return violation_count

def save_results_to_csv(results, filename):
    """結果をCSVファイルに保存する関数"""
    os.makedirs('result', exist_ok=True)

    df = pd.DataFrame(results)
    csv_path = f'result/{filename}'
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    print(f"結果をCSVファイルに保存: {csv_path}")
    return csv_path


def calculate_summary_statistics(results_data, title="統計サマリー"):
    """結果データの統計情報を計算する関数"""
    if not results_data:
        print("統計計算するデータがありません。")
        return None

    df = pd.DataFrame(results_data)

    # 数値列のみを選択
    numeric_columns = df.select_dtypes(include=[np.number]).columns

    if len(numeric_columns) == 0:
        print("数値データが見つかりません。")
        return None

    # 統計情報を計算
    summary_stats = {
        '項目': [],
        '平均': [],
        '中央値': [],
        '標準偏差': [],
        '最小値': [],
        '最大値': [],
        '合計': []
    }

    for col in numeric_columns:
        if col in df.columns:
            summary_stats['項目'].append(col)
            summary_stats['平均'].append(df[col].mean())
            summary_stats['中央値'].append(df[col].median())
            summary_stats['標準偏差'].append(df[col].std())
            summary_stats['最小値'].append(df[col].min())
            summary_stats['最大値'].append(df[col].max())
            summary_stats['合計'].append(df[col].sum())

    summary_df = pd.DataFrame(summary_stats)

    # 統計情報をCSVに保存
    stats_csv = save_results_to_csv(summary_df.to_dict('records'), f'{title}_statistics.csv')

    # 統計情報を表示
    print(f"\n=== {title} ===")
    print(summary_df.to_string(index=False))

    return summary_df, stats_csv